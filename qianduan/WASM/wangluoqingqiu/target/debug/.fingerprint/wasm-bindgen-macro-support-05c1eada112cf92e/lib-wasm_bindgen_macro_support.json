{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 1300506145316312068, "path": 15314153752754714023, "deps": [[3060637413840920116, "proc_macro2", false, 1822972885335679792], [4974441333307933176, "syn", false, 10922445589489686142], [14299170049494554845, "wasm_bindgen_shared", false, 18305810499330229518], [14372503175394433084, "wasm_bindgen_backend", false, 4571633940406132474], [17990358020177143287, "quote", false, 7886860578307854112]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-macro-support-05c1eada112cf92e/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}