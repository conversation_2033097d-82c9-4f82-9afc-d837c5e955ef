{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 1300506145316312068, "path": 659185183169397659, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 9842758103967756268], [17990358020177143287, "quote", false, 7886860578307854112]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-macro-7096d6c4ec2964bb/dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}