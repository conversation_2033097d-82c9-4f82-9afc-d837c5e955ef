{"rustc": 15597765236515928571, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 17865070097449935239, "path": 6004823689475321499, "deps": [[2828590642173593838, "cfg_if", false, 10066695395812559584], [3722963349756955755, "once_cell", false, 10867367503794095907], [6946689283190175495, "build_script_build", false, 6734978365526266965], [7858942147296547339, "rustversion", false, 2765423076720632740], [11382113702854245495, "wasm_bindgen_macro", false, 6543823406302109884]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-8b79cfbf31938baf/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}