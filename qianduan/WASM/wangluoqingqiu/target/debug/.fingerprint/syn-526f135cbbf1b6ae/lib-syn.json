{"rustc": 15597765236515928571, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 13886609198591036765, "deps": [[1988483478007900009, "unicode_ident", false, 3450573665318975097], [3060637413840920116, "proc_macro2", false, 1822972885335679792], [17990358020177143287, "quote", false, 7886860578307854112]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-526f135cbbf1b6ae/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}