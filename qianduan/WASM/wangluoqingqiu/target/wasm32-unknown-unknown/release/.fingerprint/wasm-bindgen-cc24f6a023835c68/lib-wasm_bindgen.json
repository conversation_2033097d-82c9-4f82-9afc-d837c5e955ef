{"rustc": 15597765236515928571, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 8678796594883496969, "path": 6004823689475321499, "deps": [[2828590642173593838, "cfg_if", false, 14440982198568884498], [3722963349756955755, "once_cell", false, 17854953938479617535], [6946689283190175495, "build_script_build", false, 7259787904326031846], [7858942147296547339, "rustversion", false, 8794013358969051053], [11382113702854245495, "wasm_bindgen_macro", false, 1937504799145750189]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/wasm-bindgen-cc24f6a023835c68/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}