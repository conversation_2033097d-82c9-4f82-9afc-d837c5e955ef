{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 4305681822225477910, "path": 15314153752754714023, "deps": [[3060637413840920116, "proc_macro2", false, 13662865490130945706], [4974441333307933176, "syn", false, 2311859594070259518], [14299170049494554845, "wasm_bindgen_shared", false, 11482356696159072847], [14372503175394433084, "wasm_bindgen_backend", false, 15408061566995004747], [17990358020177143287, "quote", false, 3505143006151846168]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-support-551807a42c25ee28/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}