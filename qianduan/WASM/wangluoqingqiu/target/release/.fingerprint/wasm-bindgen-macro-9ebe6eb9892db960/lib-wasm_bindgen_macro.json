{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 4305681822225477910, "path": 659185183169397659, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 7664662397648232525], [17990358020177143287, "quote", false, 3505143006151846168]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-9ebe6eb9892db960/dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}