/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const chush<PERSON><PERSON><PERSON><PERSON>n: (a: number, b: number) => void;
export const get_qingqiu: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => any;
export const post_qingqiu: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => any;
export const main: () => void;
export const __wbindgen_exn_store: (a: number) => void;
export const __externref_table_alloc: () => number;
export const __wbindgen_export_2: WebAssembly.Table;
export const __wbindgen_export_3: WebAssembly.Table;
export const __wbindgen_malloc: (a: number, b: number) => number;
export const __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
export const _dyn_core__ops__function__FnMut_____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h4da4bba0610f4fe5: (a: number, b: number) => void;
export const closure25_externref_shim: (a: number, b: number, c: any) => void;
export const closure47_externref_shim: (a: number, b: number, c: any, d: any) => void;
export const __wbindgen_start: () => void;
