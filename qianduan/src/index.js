import React from 'react';
import ReactDOM from 'react-dom/client';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import ReactPlugin from '@stagewise-plugins/react';
import { useRequest } from 'ahooks';
import init, { chushihuawangguan, get_qingqiu } from './qudong/wangluoqingqiu/wangluoqingqiu.js';

// 前端初始化函数 - 移到组件外部防止重复创建
async function qianduanchushihua() {
  try {
    console.log('开始初始化WASM模块...');
    await init();
    console.log('LUO-WASM-RUST-ROBAIKE 200！');

    // 等待一小段时间确保模块完全加载
    await new Promise(resolve => setTimeout(resolve, 100));

    // 初始化网关地址
    console.log('初始化网关地址...');
    chushihuawangguan('http://127.0.0.1:8098');
    console.log('网关地址初始化完成');

    // 发送GET请求
    console.log('发送网络请求...');
    const xiangying = await get_qingqiu(
      '/jiekou/wangzhanjichuxinxi',  // 路由
      null,                         // GET参数
      false,                        // 是否解密响应
      false,                        // 是否加密请求
      5000,                         // 超时时间：5秒
      1                             // 重试次数：1次
    );

    console.log('网站基础信息响应:', xiangying);
    return xiangying;

  } catch (cuowu) {
    console.error('前端初始化失败:', cuowu);
    console.error('错误详情:', cuowu.stack);
    throw cuowu;
  }
}

function App() {
  // 使用useRequest管理前端初始化
  const { loading, error } = useRequest(qianduanchushihua, {
    manual: false, // 自动执行
    cacheKey: 'wasm-init', // 缓存key，防止重复初始化
    staleTime: Infinity, // 永不过期，确保只初始化一次
  });

  return (
    <div>
      <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
      <div style={{ padding: '20px' }}>
        <h1>前端初始化测试</h1>
        {loading && <p>正在初始化WASM模块...</p>}
        {error && <p style={{ color: 'red' }}>初始化失败: {error.message}</p>}
        {!loading && !error && <p style={{ color: 'green' }}>初始化成功！请查看控制台输出</p>}
      </div>
    </div>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
